// Main JavaScript for Smart POS System

class SmartPOS {
    constructor() {
        this.currentUser = null;
        this.currentPage = 'dashboard';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadUserData();
        this.initializePage();
    }

    setupEventListeners() {
        // Mobile menu toggle
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        const closeSidebar = document.getElementById('closeSidebar');

        if (menuToggle) {
            menuToggle.addEventListener('click', () => this.toggleSidebar());
        }

        if (closeSidebar) {
            closeSidebar.addEventListener('click', () => this.closeSidebar());
        }

        if (overlay) {
            overlay.addEventListener('click', () => this.closeSidebar());
        }

        // Navigation links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                if (page) {
                    this.navigateToPage(page);
                }
            });
        });

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.logout());
        }

        // Window resize handler
        window.addEventListener('resize', () => this.handleResize());
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.remove('closed');
            overlay.classList.add('show');
        }
    }

    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.add('closed');
            overlay.classList.remove('show');
        }
    }

    handleResize() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        
        if (window.innerWidth >= 768) {
            if (sidebar) sidebar.classList.remove('closed');
            if (overlay) overlay.classList.remove('show');
        } else {
            if (sidebar) sidebar.classList.add('closed');
        }
    }

    navigateToPage(page) {
        this.currentPage = page;
        this.updateActiveNavigation(page);
        this.updatePageTitle(page);
        this.loadPageContent(page);
        
        // Close mobile menu
        if (window.innerWidth < 768) {
            this.closeSidebar();
        }
    }

    updateActiveNavigation(page) {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-page') === page) {
                link.classList.add('active');
            }
        });
    }

    updatePageTitle(page) {
        const titles = {
            'dashboard': 'Dashboard',
            'pos': 'Point of Sale',
            'inventory': 'Inventory Management',
            'reports': 'Reports & Analytics',
            'settings': 'Settings'
        };
        
        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle) {
            pageTitle.textContent = titles[page] || 'Smart POS';
        }
        
        document.title = `Smart POS - ${titles[page] || 'Dashboard'}`;
    }

    loadPageContent(page) {
        // Hide all content sections
        const contentSections = document.querySelectorAll('[id$="Content"]');
        contentSections.forEach(section => {
            section.classList.add('hidden');
        });
        
        // Show selected content section
        const targetContent = document.getElementById(page + 'Content');
        if (targetContent) {
            targetContent.classList.remove('hidden');
        }
        
        // Load page-specific functionality
        switch(page) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'pos':
                this.initializePOS();
                break;
            case 'inventory':
                this.loadInventoryData();
                break;
            case 'reports':
                this.loadReportsData();
                break;
            case 'settings':
                this.loadSettingsData();
                break;
        }
    }

    loadUserData() {
        // Load user data from localStorage or API
        const userData = localStorage.getItem('user');
        if (userData) {
            this.currentUser = JSON.parse(userData);
        } else {
            // Default user for demo
            this.currentUser = {
                id: 'admin_id',
                username: 'admin',
                fullName: 'Administrator',
                role: 'admin'
            };
        }
        
        this.updateUserDisplay();
    }

    updateUserDisplay() {
        const userName = document.getElementById('userName');
        const userRole = document.getElementById('userRole');
        const welcomeText = document.getElementById('welcomeText');
        
        if (userName) userName.textContent = this.currentUser.fullName;
        if (userRole) {
            userRole.textContent = this.currentUser.role;
            userRole.className = 'user-role';
            if (this.currentUser.role === 'admin') {
                userRole.style.backgroundColor = '#ebf8ff';
                userRole.style.color = '#2b6cb0';
            } else {
                userRole.style.backgroundColor = '#f0fff4';
                userRole.style.color = '#276749';
            }
        }
        if (welcomeText) welcomeText.textContent = `Welcome, ${this.currentUser.fullName}`;
    }

    loadDashboardData() {
        // Load dashboard statistics
        this.updateDashboardStats();
    }

    updateDashboardStats() {
        // Mock data - replace with actual API calls
        const stats = {
            totalProducts: 150,
            lowStockItems: 12,
            todaySales: 45,
            totalRevenue: 2850.50
        };
        
        // Update stat cards if they exist
        const statElements = {
            'totalProducts': document.querySelector('[data-stat="totalProducts"]'),
            'lowStockItems': document.querySelector('[data-stat="lowStockItems"]'),
            'todaySales': document.querySelector('[data-stat="todaySales"]'),
            'totalRevenue': document.querySelector('[data-stat="totalRevenue"]')
        };
        
        Object.keys(statElements).forEach(key => {
            const element = statElements[key];
            if (element) {
                const value = key === 'totalRevenue' ? `$${stats[key].toFixed(2)}` : stats[key];
                element.textContent = value;
            }
        });
    }

    initializePOS() {
        console.log('Initializing POS interface...');
        // POS-specific initialization code would go here
    }

    loadInventoryData() {
        console.log('Loading inventory data...');
        // Inventory-specific code would go here
    }

    loadReportsData() {
        console.log('Loading reports data...');
        // Reports-specific code would go here
    }

    loadSettingsData() {
        console.log('Loading settings data...');
        // Settings-specific code would go here
    }

    logout() {
        if (confirm('Are you sure you want to logout?')) {
            localStorage.removeItem('user');
            localStorage.removeItem('token');
            window.location.href = '/login';
        }
    }

    initializePage() {
        // Initialize sidebar state for mobile
        if (window.innerWidth < 768) {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) sidebar.classList.add('closed');
        }
        
        // Load initial page
        this.navigateToPage('dashboard');
    }
}

// API Helper functions
const API = {
    baseURL: window.location.origin,
    
    async request(endpoint, options = {}) {
        const token = localStorage.getItem('token');
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            },
            ...options
        };
        
        try {
            const response = await fetch(`${this.baseURL}${endpoint}`, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Request failed');
            }
            
            return data;
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    },
    
    async get(endpoint) {
        return this.request(endpoint);
    },
    
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
};

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.smartPOS = new SmartPOS();
});
