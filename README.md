# Smart POS System

A full-stack Point of Sale web application for small retail shops.

## Features

- Inventory management (products, suppliers)
- POS interface with barcode support
- User roles (Admin, Cashier)
- Invoice generation
- Low stock alerts

## Technologies

- Frontend: React.js, Tailwind CSS
- Backend: Node.js, Express.js
- Database: MongoDB
- Authentication: JWT

## Setup

### Prerequisites

- Node.js (v14+)
- MongoDB Atlas account or local MongoDB
- Git

### Backend Setup

1. Clone the repository
2. Navigate to backend folder: `cd backend`
3. Install dependencies: `npm install`
4. Create `.env` file with the following variables: