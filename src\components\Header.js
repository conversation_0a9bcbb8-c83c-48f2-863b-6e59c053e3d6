import React from 'react';
import { useAuth } from '../context/AuthContext';

const Header = ({ onMenuClick }) => {
  const { user, logout } = useAuth();

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center">
          <button
            onClick={onMenuClick}
            className="md:hidden text-gray-600 hover:text-gray-900 mr-4"
          >
            <i className="fas fa-bars text-xl"></i>
          </button>
          <h2 className="text-xl font-semibold text-gray-800">
            {getPageTitle(window.location.pathname)}
          </h2>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-600">
            <span className="font-medium">{user?.fullName}</span>
            <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
              {user?.role}
            </span>
          </div>
          
          <button
            onClick={logout}
            className="text-gray-600 hover:text-gray-900 transition-colors"
            title="Logout"
          >
            <i className="fas fa-sign-out-alt text-lg"></i>
          </button>
        </div>
      </div>
    </header>
  );
};

const getPageTitle = (pathname) => {
  const titles = {
    '/dashboard': 'Dashboard',
    '/pos': 'Point of Sale',
    '/inventory': 'Inventory Management',
    '/reports': 'Reports & Analytics',
    '/settings': 'Settings',
  };
  
  return titles[pathname] || 'Smart POS';
};

export default Header;
