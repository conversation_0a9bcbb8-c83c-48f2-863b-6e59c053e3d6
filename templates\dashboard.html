{% extends "base.html" %}

{% block title %}Smart POS - Dashboard{% endblock %}

{% block content %}
<!-- Sidebar -->
<div id="sidebar" class="sidebar">
    <div class="sidebar-header">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <h1 class="sidebar-title">Smart POS</h1>
            <button id="closeSidebar" style="display: none; background: none; border: none; color: white; font-size: 1.25rem; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <p id="welcomeText" class="sidebar-welcome">Welcome, Administrator</p>
    </div>
    
    <nav class="sidebar-nav">
        <a href="#" data-page="dashboard" class="nav-link active">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
        <a href="#" data-page="pos" class="nav-link">
            <i class="fas fa-cash-register"></i>
            POS
        </a>
        <a href="#" data-page="inventory" class="nav-link">
            <i class="fas fa-boxes"></i>
            Inventory
        </a>
        <a href="#" data-page="reports" class="nav-link">
            <i class="fas fa-chart-bar"></i>
            Reports
        </a>
        <a href="#" data-page="settings" class="nav-link">
            <i class="fas fa-cog"></i>
            Settings
        </a>
    </nav>
</div>

<!-- Mobile overlay -->
<div id="overlay" class="overlay"></div>

<!-- Main Content -->
<div class="main-content">
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <button id="menuToggle" class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h2 id="pageTitle" class="page-title">Dashboard</h2>
            </div>
            
            <div class="header-right">
                <div class="user-info">
                    <span id="userName" class="user-name">Administrator</span>
                    <span id="userRole" class="user-role">admin</span>
                </div>
                
                <button id="logoutBtn" class="logout-btn" title="Logout">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Content Area -->
    <main class="content-area">
        <!-- Dashboard Content -->
        <div id="dashboardContent">
            <div class="card">
                <h1 class="card-title">Welcome back, Administrator!</h1>
                <p class="card-subtitle">Here's what's happening with your store today.</p>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon blue">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Total Products</h3>
                        <p data-stat="totalProducts">150</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon yellow">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Low Stock Items</h3>
                        <p data-stat="lowStockItems">12</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon green">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Today's Sales</h3>
                        <p data-stat="todaySales">45</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon purple">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-content">
                        <h3>Total Revenue</h3>
                        <p data-stat="totalRevenue">$2,850.50</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions and Info Grid -->
            <div class="grid-3">
                <div class="card">
                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #2d3748; margin-bottom: 1rem;">Quick Actions</h3>
                    <div class="space-y-3">
                        <button class="btn btn-primary" style="width: 100%; justify-content: flex-start;">
                            <i class="fas fa-plus"></i>
                            Add New Product
                        </button>
                        <button class="btn btn-secondary" style="width: 100%; justify-content: flex-start;">
                            <i class="fas fa-cash-register"></i>
                            Start New Sale
                        </button>
                        <button class="btn btn-secondary" style="width: 100%; justify-content: flex-start;">
                            <i class="fas fa-chart-line"></i>
                            View Reports
                        </button>
                    </div>
                </div>

                <div class="card">
                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #2d3748; margin-bottom: 1rem;">Recent Activity</h3>
                    <div class="space-y-3" style="font-size: 0.875rem;">
                        <div style="display: flex; align-items: center; color: #4a5568;">
                            <i class="fas fa-shopping-cart" style="color: #38a169; margin-right: 0.5rem;"></i>
                            Sale completed - $45.99
                        </div>
                        <div style="display: flex; align-items: center; color: #4a5568;">
                            <i class="fas fa-plus" style="color: #3182ce; margin-right: 0.5rem;"></i>
                            Product added - Coffee Beans
                        </div>
                        <div style="display: flex; align-items: center; color: #4a5568;">
                            <i class="fas fa-exclamation-triangle" style="color: #d69e2e; margin-right: 0.5rem;"></i>
                            Low stock alert - Milk
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #2d3748; margin-bottom: 1rem;">System Status</h3>
                    <div class="space-y-3">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span style="font-size: 0.875rem; color: #4a5568;">Database</span>
                            <span style="color: #38a169;">
                                <i class="fas fa-check-circle" style="margin-right: 0.25rem;"></i>
                                Connected
                            </span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span style="font-size: 0.875rem; color: #4a5568;">Last Backup</span>
                            <span style="color: #4a5568; font-size: 0.875rem;">2 hours ago</span>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span style="font-size: 0.875rem; color: #4a5568;">Version</span>
                            <span style="color: #4a5568; font-size: 0.875rem;">v1.0.0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- POS Content (hidden by default) -->
        <div id="posContent" class="hidden">
            <div class="card">
                <h1 class="card-title">Point of Sale</h1>
                <p class="card-subtitle">POS interface for processing sales and transactions.</p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; margin-top: 1.5rem;">
                    <div style="border: 2px dashed #cbd5e0; border-radius: 0.5rem; padding: 2rem; text-align: center;">
                        <i class="fas fa-search" style="font-size: 2.5rem; color: #a0aec0; margin-bottom: 1rem;"></i>
                        <p style="color: #718096;">Product Search Area</p>
                    </div>
                    <div style="border: 2px dashed #cbd5e0; border-radius: 0.5rem; padding: 2rem; text-align: center;">
                        <i class="fas fa-shopping-cart" style="font-size: 2.5rem; color: #a0aec0; margin-bottom: 1rem;"></i>
                        <p style="color: #718096;">Shopping Cart Area</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Content (hidden by default) -->
        <div id="inventoryContent" class="hidden">
            <div class="card">
                <h1 class="card-title">Inventory Management</h1>
                <p class="card-subtitle">Manage your products, stock levels, and suppliers.</p>
            </div>
        </div>

        <!-- Reports Content (hidden by default) -->
        <div id="reportsContent" class="hidden">
            <div class="card">
                <h1 class="card-title">Reports & Analytics</h1>
                <p class="card-subtitle">View sales reports, analytics, and business insights.</p>
            </div>
        </div>

        <!-- Settings Content (hidden by default) -->
        <div id="settingsContent" class="hidden">
            <div class="card">
                <h1 class="card-title">Settings</h1>
                <p class="card-subtitle">Configure system settings and preferences.</p>
            </div>
        </div>
    </main>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Page-specific JavaScript can go here
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Dashboard page loaded');
    });
</script>
{% endblock %}
