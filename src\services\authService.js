import api from './api';

export const login = async (credentials) => {
  try {
    const response = await api.post('/auth/login', credentials);
    const { token, user } = response.data;
    
    // Store token in localStorage
    localStorage.setItem('token', token);
    localStorage.setItem('user', JSON.stringify(user));
    
    return user;
  } catch (error) {
    throw new Error(error.response?.data?.error || 'Login failed');
  }
};

export const logout = async () => {
  // Remove token and user data from localStorage
  localStorage.removeItem('token');
  localStorage.removeItem('user');
};

export const getCurrentUser = async () => {
  const token = localStorage.getItem('token');
  const userData = localStorage.getItem('user');
  
  if (!token || !userData) {
    throw new Error('No authentication data found');
  }
  
  try {
    // Verify token is still valid by making a test request
    await api.get('/auth/verify');
    return JSON.parse(userData);
  } catch (error) {
    // Token is invalid, clear storage
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    throw new Error('Authentication expired');
  }
};

export const isAuthenticated = () => {
  return !!localStorage.getItem('token');
};

export const getToken = () => {
  return localStorage.getItem('token');
};
