{% extends "base.html" %}

{% block title %}Smart POS - Point of Sale{% endblock %}

{% block extra_css %}
<style>
    .pos-container {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 1.5rem;
        height: calc(100vh - 80px);
    }
    
    @media (max-width: 1024px) {
        .pos-container {
            grid-template-columns: 1fr;
            height: auto;
        }
    }
    
    .product-search {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        height: fit-content;
    }
    
    .search-input {
        width: 100%;
        padding: 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 0.5rem;
        font-size: 1.125rem;
        margin-bottom: 1rem;
    }
    
    .search-input:focus {
        outline: none;
        border-color: #3182ce;
        box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
    }
    
    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
        max-height: 500px;
        overflow-y: auto;
    }
    
    .product-card {
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .product-card:hover {
        border-color: #3182ce;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .product-name {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2d3748;
    }
    
    .product-price {
        font-size: 1.125rem;
        font-weight: bold;
        color: #38a169;
        margin-bottom: 0.25rem;
    }
    
    .product-stock {
        font-size: 0.875rem;
        color: #4a5568;
    }
    
    .cart-section {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        height: fit-content;
        max-height: calc(100vh - 120px);
        overflow-y: auto;
    }
    
    .cart-header {
        font-size: 1.25rem;
        font-weight: bold;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .cart-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f7fafc;
    }
    
    .cart-item-info {
        flex: 1;
    }
    
    .cart-item-name {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }
    
    .cart-item-price {
        color: #4a5568;
        font-size: 0.875rem;
    }
    
    .quantity-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0.5rem 0;
    }
    
    .qty-btn {
        width: 30px;
        height: 30px;
        border: 1px solid #e2e8f0;
        background: white;
        border-radius: 0.25rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .qty-btn:hover {
        background: #f7fafc;
    }
    
    .qty-input {
        width: 60px;
        text-align: center;
        border: 1px solid #e2e8f0;
        border-radius: 0.25rem;
        padding: 0.25rem;
    }
    
    .cart-totals {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 2px solid #e2e8f0;
    }
    
    .total-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }
    
    .total-final {
        font-size: 1.25rem;
        font-weight: bold;
        color: #2d3748;
    }
    
    .checkout-btn {
        width: 100%;
        background: #38a169;
        color: white;
        padding: 1rem;
        border: none;
        border-radius: 0.5rem;
        font-size: 1.125rem;
        font-weight: 600;
        cursor: pointer;
        margin-top: 1rem;
    }
    
    .checkout-btn:hover {
        background: #2f855a;
    }
    
    .checkout-btn:disabled {
        background: #a0aec0;
        cursor: not-allowed;
    }
    
    .empty-cart {
        text-align: center;
        color: #a0aec0;
        padding: 2rem;
    }
    
    .loading {
        text-align: center;
        color: #4a5568;
        padding: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<header class="header">
    <div class="header-content">
        <div class="header-left">
            <h2 class="page-title">Point of Sale</h2>
        </div>
        
        <div class="header-right">
            <div class="user-info">
                <span id="userName" class="user-name">Cashier</span>
                <span id="userRole" class="user-role">cashier</span>
            </div>
            
            <button id="logoutBtn" class="logout-btn" title="Logout">
                <i class="fas fa-sign-out-alt"></i>
            </button>
        </div>
    </div>
</header>

<!-- POS Interface -->
<main class="content-area">
    <div class="pos-container">
        <!-- Product Search Section -->
        <div class="product-search">
            <h3 style="margin-bottom: 1rem; font-size: 1.125rem; font-weight: 600;">Search Products</h3>
            
            <input 
                type="text" 
                id="searchInput" 
                class="search-input" 
                placeholder="Scan barcode or enter product name/ID..."
                autocomplete="off"
            >
            
            <div id="searchResults">
                <div id="loadingMessage" class="loading" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> Searching...
                </div>
                
                <div id="productsGrid" class="products-grid">
                    <!-- Products will be loaded here -->
                </div>
                
                <div id="noResults" style="display: none; text-align: center; color: #a0aec0; padding: 2rem;">
                    <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>No products found. Try a different search term.</p>
                </div>
            </div>
        </div>
        
        <!-- Cart Section -->
        <div class="cart-section">
            <div class="cart-header">
                <i class="fas fa-shopping-cart"></i> Shopping Cart
            </div>
            
            <div id="cartItems">
                <div id="emptyCart" class="empty-cart">
                    <i class="fas fa-shopping-cart" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>Cart is empty</p>
                    <p style="font-size: 0.875rem;">Scan or search for products to add them</p>
                </div>
            </div>
            
            <div id="cartTotals" class="cart-totals" style="display: none;">
                <div class="total-row">
                    <span>Subtotal:</span>
                    <span id="subtotal">$0.00</span>
                </div>
                <div class="total-row">
                    <span>Tax (10%):</span>
                    <span id="tax">$0.00</span>
                </div>
                <div class="total-row total-final">
                    <span>Total:</span>
                    <span id="total">$0.00</span>
                </div>
                
                <div style="margin-top: 1rem;">
                    <label style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Payment Method:</label>
                    <select id="paymentMethod" style="width: 100%; padding: 0.5rem; border: 1px solid #e2e8f0; border-radius: 0.25rem;">
                        <option value="cash">Cash</option>
                        <option value="card">Card</option>
                    </select>
                </div>
                
                <button id="checkoutBtn" class="checkout-btn">
                    <i class="fas fa-credit-card"></i> Complete Sale
                </button>
            </div>
        </div>
    </div>
</main>
{% endblock %}

{% block extra_js %}
<script>
class POSSystem {
    constructor() {
        this.cart = [];
        this.products = [];
        this.searchTimeout = null;
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadUserData();
        this.loadAllProducts();
    }
    
    setupEventListeners() {
        const searchInput = document.getElementById('searchInput');
        const checkoutBtn = document.getElementById('checkoutBtn');
        const logoutBtn = document.getElementById('logoutBtn');
        
        // Search functionality
        searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.searchProducts(e.target.value);
            }, 300);
        });
        
        // Barcode scanner simulation (Enter key)
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.searchProducts(e.target.value);
            }
        });
        
        // Checkout
        if (checkoutBtn) {
            checkoutBtn.addEventListener('click', () => this.checkout());
        }
        
        // Logout
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.logout());
        }
    }
    
    loadUserData() {
        const userData = localStorage.getItem('user');
        if (userData) {
            const user = JSON.parse(userData);
            document.getElementById('userName').textContent = user.fullName;
            document.getElementById('userRole').textContent = user.role;
        }
    }
    
    async loadAllProducts() {
        try {
            const response = await fetch('/api/inventory/products', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });
            
            if (response.ok) {
                this.products = await response.json();
                this.displayProducts(this.products);
            }
        } catch (error) {
            console.error('Error loading products:', error);
        }
    }
    
    async searchProducts(query) {
        if (!query.trim()) {
            this.displayProducts(this.products);
            return;
        }
        
        this.showLoading(true);
        
        try {
            const response = await fetch(`/api/pos/products/search?query=${encodeURIComponent(query)}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });
            
            if (response.ok) {
                const products = await response.json();
                this.displayProducts(products);
            }
        } catch (error) {
            console.error('Search error:', error);
        } finally {
            this.showLoading(false);
        }
    }
    
    displayProducts(products) {
        const grid = document.getElementById('productsGrid');
        const noResults = document.getElementById('noResults');
        
        if (products.length === 0) {
            grid.innerHTML = '';
            noResults.style.display = 'block';
            return;
        }
        
        noResults.style.display = 'none';
        grid.innerHTML = products.map(product => `
            <div class="product-card" onclick="pos.addToCart('${product._id}')">
                <div class="product-name">${product.name}</div>
                <div class="product-price">$${product.price.toFixed(2)}</div>
                <div class="product-stock">Stock: ${product.stock}</div>
                ${product.barcode ? `<div style="font-size: 0.75rem; color: #a0aec0;">ID: ${product.barcode}</div>` : ''}
            </div>
        `).join('');
    }
    
    showLoading(show) {
        document.getElementById('loadingMessage').style.display = show ? 'block' : 'none';
    }
    
    addToCart(productId) {
        const product = this.products.find(p => p._id === productId);
        if (!product) return;
        
        if (product.stock <= 0) {
            alert('Product is out of stock!');
            return;
        }
        
        const existingItem = this.cart.find(item => item.productId === productId);
        
        if (existingItem) {
            if (existingItem.quantity >= product.stock) {
                alert('Cannot add more items. Insufficient stock!');
                return;
            }
            existingItem.quantity += 1;
        } else {
            this.cart.push({
                productId: productId,
                name: product.name,
                price: product.price,
                quantity: 1,
                maxStock: product.stock
            });
        }
        
        this.updateCartDisplay();
    }
    
    removeFromCart(productId) {
        this.cart = this.cart.filter(item => item.productId !== productId);
        this.updateCartDisplay();
    }
    
    updateQuantity(productId, newQuantity) {
        const item = this.cart.find(item => item.productId === productId);
        if (!item) return;
        
        if (newQuantity <= 0) {
            this.removeFromCart(productId);
            return;
        }
        
        if (newQuantity > item.maxStock) {
            alert('Cannot exceed available stock!');
            return;
        }
        
        item.quantity = newQuantity;
        this.updateCartDisplay();
    }
    
    updateCartDisplay() {
        const cartItems = document.getElementById('cartItems');
        const cartTotals = document.getElementById('cartTotals');
        const emptyCart = document.getElementById('emptyCart');
        
        if (this.cart.length === 0) {
            emptyCart.style.display = 'block';
            cartTotals.style.display = 'none';
            cartItems.innerHTML = '<div id="emptyCart" class="empty-cart"><i class="fas fa-shopping-cart" style="font-size: 2rem; margin-bottom: 1rem;"></i><p>Cart is empty</p><p style="font-size: 0.875rem;">Scan or search for products to add them</p></div>';
            return;
        }
        
        emptyCart.style.display = 'none';
        cartTotals.style.display = 'block';
        
        cartItems.innerHTML = this.cart.map(item => `
            <div class="cart-item">
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-price">$${item.price.toFixed(2)} each</div>
                    <div class="quantity-controls">
                        <button class="qty-btn" onclick="pos.updateQuantity('${item.productId}', ${item.quantity - 1})">-</button>
                        <input type="number" class="qty-input" value="${item.quantity}" 
                               onchange="pos.updateQuantity('${item.productId}', parseInt(this.value))"
                               min="1" max="${item.maxStock}">
                        <button class="qty-btn" onclick="pos.updateQuantity('${item.productId}', ${item.quantity + 1})">+</button>
                    </div>
                </div>
                <div>
                    <div style="font-weight: bold;">$${(item.price * item.quantity).toFixed(2)}</div>
                    <button onclick="pos.removeFromCart('${item.productId}')" 
                            style="color: #e53e3e; background: none; border: none; cursor: pointer; font-size: 0.875rem;">
                        <i class="fas fa-trash"></i> Remove
                    </button>
                </div>
            </div>
        `).join('');
        
        this.updateTotals();
    }
    
    updateTotals() {
        const subtotal = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const tax = subtotal * 0.1;
        const total = subtotal + tax;
        
        document.getElementById('subtotal').textContent = `$${subtotal.toFixed(2)}`;
        document.getElementById('tax').textContent = `$${tax.toFixed(2)}`;
        document.getElementById('total').textContent = `$${total.toFixed(2)}`;
    }
    
    async checkout() {
        if (this.cart.length === 0) {
            alert('Cart is empty!');
            return;
        }
        
        const paymentMethod = document.getElementById('paymentMethod').value;
        const checkoutBtn = document.getElementById('checkoutBtn');
        
        checkoutBtn.disabled = true;
        checkoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        
        try {
            const response = await fetch('/api/pos/invoices', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    items: this.cart,
                    paymentMethod: paymentMethod
                })
            });
            
            if (response.ok) {
                const invoice = await response.json();
                alert(`Sale completed successfully!\nInvoice: ${invoice.invoiceNumber}\nTotal: $${invoice.total.toFixed(2)}`);
                
                // Clear cart and refresh products
                this.cart = [];
                this.updateCartDisplay();
                this.loadAllProducts();
                document.getElementById('searchInput').value = '';
            } else {
                const error = await response.json();
                alert(`Error: ${error.error}`);
            }
        } catch (error) {
            console.error('Checkout error:', error);
            alert('Checkout failed. Please try again.');
        } finally {
            checkoutBtn.disabled = false;
            checkoutBtn.innerHTML = '<i class="fas fa-credit-card"></i> Complete Sale';
        }
    }
    
    logout() {
        if (confirm('Are you sure you want to logout?')) {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.href = '/login';
        }
    }
}

// Initialize POS system
const pos = new POSSystem();
</script>
{% endblock %}
