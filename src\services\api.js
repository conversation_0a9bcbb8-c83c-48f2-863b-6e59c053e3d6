const API_BASE = process.env.REACT_APP_API_URL || 'http://localhost:5000';

export const login = async (credentials) => {
  const response = await fetch(`${API_BASE}/api/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(credentials)
  });
  return await response.json();
};

export const getProducts = async (token) => {
  const response = await fetch(`${API_BASE}/api/inventory/products`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return await response.json();
};

export const createInvoice = async (invoiceData, token) => {
  const response = await fetch(`${API_BASE}/api/pos/invoices`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(invoiceData)
  });
  return await response.json();
};