from flask import Flask, request, jsonify
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, jwt_required, create_access_token, get_jwt_identity
from flask_pymongo import PyMongo
from flask_cors import CORS
from dotenv import load_dotenv
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
app.config["MONGO_URI"] = os.getenv("MONGO_URI", "mongodb://localhost:27017/smart_pos")
app.config["JWT_SECRET_KEY"] = os.getenv("JWT_SECRET", "super-secret-key")
app.config["JWT_ACCESS_TOKEN_EXPIRES"] = timedelta(days=30)

# Initialize extensions
mongo = PyMongo(app)
jwt = JWTManager(app)

# Helper functions
def serialize_doc(doc):
    if not doc:
        return None
    doc['_id'] = str(doc['_id'])
    return doc

# Routes
@app.route('/')
def home():
    return jsonify({"message": "Smart POS System API"})

@app.route('/api/auth/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')

    user = mongo.db.users.find_one({'username': username})
    
    if not user or not check_password_hash(user['password'], password):
        return jsonify({"error": "Invalid credentials"}), 401

    access_token = create_access_token(identity={
        "id": str(user['_id']),
        "role": user['role']
    })

    return jsonify({
        "token": access_token,
        "user": {
            "id": str(user['_id']),
            "username": user['username'],
            "role": user['role'],
            "fullName": user['fullName']
        }
    })

@app.route('/api/inventory/products', methods=['GET'])
@jwt_required()
def get_products():
    products = list(mongo.db.products.find())
    return jsonify([serialize_doc(p) for p in products])

@app.route('/api/inventory/products', methods=['POST'])
@jwt_required()
def create_product():
    if get_jwt_identity()['role'] != 'admin':
        return jsonify({"error": "Admin access required"}), 403

    data = request.get_json()
    data['createdAt'] = datetime.now()
    
    result = mongo.db.products.insert_one(data)
    data['_id'] = str(result.inserted_id)
    
    return jsonify(serialize_doc(data)), 201

@app.route('/api/pos/invoices', methods=['POST'])
@jwt_required()
def create_invoice():
    data = request.get_json()
    user_id = get_jwt_identity()['id']
    
    subtotal = 0
    items = []

    for item in data['items']:
        product = mongo.db.products.find_one({'_id': item['productId']})
        if not product:
            return jsonify({"error": f"Product {item['productId']} not found"}), 400

        if product['stock'] < item['quantity']:
            return jsonify({"error": f"Insufficient stock for {product['name']}"}), 400

        item_total = product['price'] * item['quantity']
        subtotal += item_total

        items.append({
            "productId": str(product['_id']),
            "name": product['name'],
            "price": product['price'],
            "quantity": item['quantity']
        })

        # Update stock
        mongo.db.products.update_one(
            {'_id': product['_id']},
            {'$inc': {'stock': -item['quantity']}}
        )

    tax = subtotal * 0.1  # 10% tax
    total = subtotal + tax

    invoice = {
        "invoiceNumber": f"INV-{int(datetime.now().timestamp())}",
        "date": datetime.now(),
        "items": items,
        "subtotal": subtotal,
        "tax": tax,
        "total": total,
        "paymentMethod": data['paymentMethod'],
        "customerName": data.get('customerName', ''),
        "customerContact": data.get('customerContact', ''),
        "cashierId": user_id
    }

    result = mongo.db.invoices.insert_one(invoice)
    invoice['_id'] = str(result.inserted_id)
    
    return jsonify(serialize_doc(invoice)), 201

@app.route('/api/auth/verify', methods=['GET'])
@jwt_required()
def verify_token():
    current_user = get_jwt_identity()
    return jsonify({"valid": True, "user": current_user})

@app.route('/api/pos/products/search', methods=['GET'])
@jwt_required()
def search_products():
    query = request.args.get('query', '').lower()

    products = list(mongo.db.products.find({
        '$or': [
            {'name': {'$regex': query, '$options': 'i'}},
            {'productId': {'$regex': query, '$options': 'i'}},
            {'barcode': query}
        ]
    }))

    return jsonify([serialize_doc(p) for p in products])

@app.errorhandler(404)
def not_found(e):
    return jsonify({"error": "Route not found"}), 404

if __name__ == '__main__':
    app.run(debug=os.getenv("FLASK_DEBUG", True), host='0.0.0.0', port=5000)
