from flask import Flask, request, jsonify, render_template, redirect, url_for
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>, jwt_required, create_access_token, get_jwt_identity
from flask_cors import CORS
from dotenv import load_dotenv
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os
import uuid

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
app.config["JWT_SECRET_KEY"] = os.getenv("JWT_SECRET", "super-secret-key")
app.config["JWT_ACCESS_TOKEN_EXPIRES"] = timedelta(days=30)

# Initialize extensions
jwt = JWTManager(app)

# In-memory database for demo (replace with MongoDB later)
users_db = {
    "admin": {
        "_id": "admin_id",
        "username": "admin",
        "password": generate_password_hash("admin123"),
        "role": "admin",
        "fullName": "Administrator"
    },
    "cashier": {
        "_id": "cashier_id",
        "username": "cashier",
        "password": generate_password_hash("cashier123"),
        "role": "cashier",
        "fullName": "Cashier User"
    }
}

products_db = {
    "1": {
        "_id": "1",
        "productId": "P001",
        "name": "Sample Product 1",
        "price": 10.99,
        "stock": 100,
        "barcode": "123456789"
    },
    "2": {
        "_id": "2",
        "productId": "P002",
        "name": "Sample Product 2",
        "price": 25.50,
        "stock": 50,
        "barcode": "987654321"
    }
}

invoices_db = {}

# Helper functions
def serialize_doc(doc):
    if not doc:
        return None
    doc['_id'] = str(doc['_id'])
    return doc

# HTML Routes
@app.route('/')
def home():
    return redirect(url_for('dashboard'))

@app.route('/login')
def login_page():
    return render_template('login.html')

@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html')

# API Routes

@app.route('/api/auth/login', methods=['POST'])
def api_login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')

    # Find user in in-memory database
    user = users_db.get(username)

    if not user or not check_password_hash(user['password'], password):
        return jsonify({"error": "Invalid credentials"}), 401

    access_token = create_access_token(identity={
        "id": user['_id'],
        "role": user['role']
    })

    return jsonify({
        "token": access_token,
        "user": {
            "id": user['_id'],
            "username": user['username'],
            "role": user['role'],
            "fullName": user['fullName']
        }
    })

@app.route('/api/inventory/products', methods=['GET'])
@jwt_required()
def get_products():
    products = list(products_db.values())
    return jsonify([serialize_doc(p) for p in products])

@app.route('/api/inventory/products', methods=['POST'])
@jwt_required()
def create_product():
    if get_jwt_identity()['role'] != 'admin':
        return jsonify({"error": "Admin access required"}), 403

    data = request.get_json()
    product_id = str(uuid.uuid4())
    data['_id'] = product_id
    data['createdAt'] = datetime.now().isoformat()
    products_db[product_id] = data

    return jsonify(serialize_doc(data)), 201

@app.route('/api/pos/invoices', methods=['POST'])
@jwt_required()
def create_invoice():
    data = request.get_json()
    user_id = get_jwt_identity()['id']
    
    subtotal = 0
    items = []

    for item in data['items']:
        product = products_db.get(item['productId'])
        if not product:
            return jsonify({"error": f"Product {item['productId']} not found"}), 400

        if product['stock'] < item['quantity']:
            return jsonify({"error": f"Insufficient stock for {product['name']}"}), 400

        item_total = product['price'] * item['quantity']
        subtotal += item_total

        items.append({
            "productId": str(product['_id']),
            "name": product['name'],
            "price": product['price'],
            "quantity": item['quantity']
        })

        # Update stock
        products_db[item['productId']]['stock'] -= item['quantity']

    tax = subtotal * 0.1  # 10% tax
    total = subtotal + tax

    invoice = {
        "invoiceNumber": f"INV-{int(datetime.now().timestamp())}",
        "date": datetime.now(),
        "items": items,
        "subtotal": subtotal,
        "tax": tax,
        "total": total,
        "paymentMethod": data['paymentMethod'],
        "customerName": data.get('customerName', ''),
        "customerContact": data.get('customerContact', ''),
        "cashierId": user_id
    }

    invoice_id = str(uuid.uuid4())
    invoice['_id'] = invoice_id
    invoice['date'] = datetime.now().isoformat()
    invoices_db[invoice_id] = invoice
    
    return jsonify(serialize_doc(invoice)), 201

@app.route('/api/auth/verify', methods=['GET'])
@jwt_required()
def verify_token():
    current_user = get_jwt_identity()
    return jsonify({"valid": True, "user": current_user})

@app.route('/api/pos/products/search', methods=['GET'])
@jwt_required()
def search_products():
    query = request.args.get('query', '').lower()

    # Simple search in in-memory database
    matching_products = []
    for product in products_db.values():
        if (query in product['name'].lower() or
            query in product.get('productId', '').lower() or
            query == product.get('barcode', '')):
            matching_products.append(product)

    products = matching_products

    return jsonify([serialize_doc(p) for p in products])

@app.errorhandler(404)
def not_found(e):
    return jsonify({"error": "Route not found"}), 404

if __name__ == '__main__':
    app.run(debug=os.getenv("FLASK_DEBUG", True), host='0.0.0.0', port=5000)
