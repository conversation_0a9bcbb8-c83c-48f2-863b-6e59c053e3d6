import React from 'react';
import { NavLink } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const Sidebar = ({ isOpen, onClose }) => {
  const { user, isAdmin } = useAuth();

  const menuItems = [
    { path: '/dashboard', icon: 'fas fa-tachometer-alt', label: 'Dashboard', roles: ['admin', 'cashier'] },
    { path: '/pos', icon: 'fas fa-cash-register', label: 'POS', roles: ['admin', 'cashier'] },
    { path: '/inventory', icon: 'fas fa-boxes', label: 'Inventory', roles: ['admin'] },
    { path: '/reports', icon: 'fas fa-chart-bar', label: 'Reports', roles: ['admin'] },
    { path: '/settings', icon: 'fas fa-cog', label: 'Settings', roles: ['admin'] },
  ];

  const filteredMenuItems = menuItems.filter(item => 
    item.roles.includes(user?.role)
  );

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
          onClick={onClose}
        ></div>
      )}
      
      {/* Sidebar */}
      <div className={`sidebar ${isOpen ? 'open' : ''} md:translate-x-0 z-30`}>
        <div className="p-6">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold">Smart POS</h1>
            <button 
              onClick={onClose}
              className="md:hidden text-white hover:text-gray-300"
            >
              <i className="fas fa-times"></i>
            </button>
          </div>
          <p className="text-gray-300 text-sm mt-2">
            Welcome, {user?.fullName}
          </p>
        </div>
        
        <nav className="mt-6">
          {filteredMenuItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) =>
                `flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors ${
                  isActive ? 'bg-gray-700 text-white border-r-4 border-blue-500' : ''
                }`
              }
              onClick={() => window.innerWidth < 768 && onClose()}
            >
              <i className={`${item.icon} w-5 mr-3`}></i>
              {item.label}
            </NavLink>
          ))}
        </nav>
      </div>
    </>
  );
};

export default Sidebar;
