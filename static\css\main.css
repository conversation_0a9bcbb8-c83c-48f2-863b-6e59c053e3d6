/* Main CSS for Smart POS System */

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background-color: #f8fafc;
    color: #1a202c;
}

/* Sidebar styles */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 16rem;
    height: 100vh;
    background-color: #2d3748;
    color: white;
    z-index: 30;
    transition: transform 0.3s ease-in-out;
}

.sidebar.closed {
    transform: translateX(-100%);
}

@media (min-width: 768px) {
    .sidebar.closed {
        transform: translateX(0);
    }
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #4a5568;
}

.sidebar-title {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.sidebar-welcome {
    color: #a0aec0;
    font-size: 0.875rem;
}

.sidebar-nav {
    margin-top: 1.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #a0aec0;
    text-decoration: none;
    transition: all 0.2s;
}

.nav-link:hover {
    background-color: #4a5568;
    color: white;
}

.nav-link.active {
    background-color: #4a5568;
    color: white;
    border-right: 4px solid #3182ce;
}

.nav-link i {
    width: 1.25rem;
    margin-right: 0.75rem;
}

/* Header styles */
.header {
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #e2e8f0;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
}

.header-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: #4a5568;
    font-size: 1.25rem;
    margin-right: 1rem;
    cursor: pointer;
}

@media (max-width: 767px) {
    .menu-toggle {
        display: block;
    }
}

.page-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    font-size: 0.875rem;
    color: #4a5568;
}

.user-name {
    font-weight: 500;
}

.user-role {
    margin-left: 0.5rem;
    padding: 0.25rem 0.5rem;
    background-color: #ebf8ff;
    color: #2b6cb0;
    border-radius: 9999px;
    font-size: 0.75rem;
}

.logout-btn {
    background: none;
    border: none;
    color: #4a5568;
    font-size: 1.125rem;
    cursor: pointer;
    transition: color 0.2s;
}

.logout-btn:hover {
    color: #2d3748;
}

/* Main content styles */
.main-content {
    margin-left: 16rem;
    min-height: 100vh;
}

@media (max-width: 767px) {
    .main-content {
        margin-left: 0;
    }
}

.content-area {
    padding: 1.5rem;
}

/* Card styles */
.card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.card-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.card-subtitle {
    color: #4a5568;
    margin-bottom: 1rem;
}

/* Stats grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    display: flex;
    align-items: center;
}

.stat-icon {
    padding: 0.75rem;
    border-radius: 50%;
    margin-right: 1rem;
    font-size: 1.25rem;
    color: white;
}

.stat-icon.blue { background-color: #3182ce; }
.stat-icon.yellow { background-color: #d69e2e; }
.stat-icon.green { background-color: #38a169; }
.stat-icon.purple { background-color: #805ad5; }

.stat-content h3 {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 0.25rem;
}

.stat-content p {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2d3748;
}

/* Button styles */
.btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
}

.btn-primary {
    background-color: #3182ce;
    color: white;
}

.btn-primary:hover {
    background-color: #2c5282;
}

.btn-secondary {
    background-color: #4a5568;
    color: white;
}

.btn-secondary:hover {
    background-color: #2d3748;
}

.btn i {
    margin-right: 0.5rem;
}

/* Overlay for mobile */
.overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 20;
    display: none;
}

.overlay.show {
    display: block;
}

/* Utility classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-4 {
    margin-bottom: 1rem;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

.grid-3 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .grid-3 {
        grid-template-columns: 1fr;
    }
    
    .header-content {
        padding: 1rem;
    }
    
    .content-area {
        padding: 1rem;
    }
}
