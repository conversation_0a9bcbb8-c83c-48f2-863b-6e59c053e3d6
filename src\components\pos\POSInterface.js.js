import { useState, useEffect } from 'react';
import { searchProducts } from '../../services/posService';
import Cart from './Cart';

const POSInterface = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [products, setProducts] = useState([]);
  const [cart, setCart] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (searchQuery.length > 2) {
      const timer = setTimeout(() => {
        handleSearch();
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [searchQuery]);

  const handleSearch = async () => {
    try {
      setLoading(true);
      const results = await searchProducts(searchQuery);
      setProducts(results);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBarcodeScan = async (barcode) => {
    try {
      setLoading(true);
      const results = await searchProducts(barcode);
      if (results.length > 0) {
        addToCart(results[0]);
      }
    } catch (error) {
      console.error('Barcode scan error:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = (product) => {
    const existingItem = cart.find(item => item.product._id === product._id);
    
    if (existingItem) {
      setCart(cart.map(item =>
        item.product._id === product._id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, { product, quantity: 1 }]);
    }
  };

  const removeFromCart = (productId) => {
    setCart(cart.filter(item => item.product._id !== productId));
  };

  const updateQuantity = (productId, quantity) => {
    if (quantity < 1) {
      removeFromCart(productId);
      return;
    }
    
    setCart(cart.map(item =>
      item.product._id === productId
        ? { ...item, quantity }
        : item
    ));
  };

  return (
    <div className="flex flex-col md:flex-row gap-4 p-4">
      <div className="md:w-2/3">
        <div className="mb-4">
          <input
            type="text"
            placeholder="Search by name, ID, or scan barcode"
            className="w-full p-2 border rounded"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        {loading && <p>Loading...</p>}
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {products.map(product => (
            <div 
              key={product._id}
              className="border p-4 rounded cursor-pointer hover:bg-gray-50"
              onClick={() => addToCart(product)}
            >
              <h3 className="font-bold">{product.name}</h3>
              <p>${product.price.toFixed(2)}</p>
              <p>Stock: {product.stock}</p>
            </div>
          ))}
        </div>
      </div>
      
      <div className="md:w-1/3">
        <Cart 
          items={cart} 
          onRemove={removeFromCart}
          onQuantityChange={updateQuantity}
          onCheckout={() => {/* Implement checkout */}}
        />
      </div>
    </div>
  );
};

export default POSInterface;