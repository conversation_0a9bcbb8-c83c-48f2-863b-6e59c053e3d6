import api from './api';

export const searchProducts = async (query) => {
  const response = await api.get('/pos/search', { params: { query } });
  return response.data;
};

export const createInvoice = async (invoiceData) => {
  const response = await api.post('/pos/invoices', invoiceData);
  return response.data;
};

export const getInvoices = async (params = {}) => {
  const response = await api.get('/pos/invoices', { params });
  return response.data;
};

export const getInvoiceById = async (id) => {
  const response = await api.get(`/pos/invoices/${id}`);
  return response.data;
};