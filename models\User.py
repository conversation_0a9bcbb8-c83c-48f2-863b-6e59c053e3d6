from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

class User:
    def __init__(self, username, password, role, fullName, email):
        self.username = username
        self.password = generate_password_hash(password)
        self.role = role
        self.fullName = fullName
        self.email = email
        self.isActive = True
        self.lastLogin = None
        self.createdAt = datetime.now()

    def verify_password(self, password):
        return check_password_hash(self.password, password)

    def to_dict(self):
        return {
            'username': self.username,
            'password': self.password,
            'role': self.role,
            'fullName': self.fullName,
            'email': self.email,
            'isActive': self.isActive,
            'lastLogin': self.lastLogin,
            'createdAt': self.createdAt
        }