{% extends "base.html" %}

{% block title %}Smart POS - Inventory{% endblock %}

{% block extra_css %}
<style>
    .inventory-container {
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .search-bar {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .search-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #e2e8f0;
        border-radius: 0.375rem;
        font-size: 1rem;
    }
    
    .inventory-table {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .table-header {
        background: #f7fafc;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e2e8f0;
        font-weight: 600;
        color: #2d3748;
    }
    
    .table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .table th,
    .table td {
        padding: 1rem 1.5rem;
        text-align: left;
        border-bottom: 1px solid #f7fafc;
    }
    
    .table th {
        background: #f7fafc;
        font-weight: 600;
        color: #4a5568;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .table tbody tr:hover {
        background: #f7fafc;
    }
    
    .stock-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .stock-high {
        background: #c6f6d5;
        color: #22543d;
    }
    
    .stock-medium {
        background: #fef5e7;
        color: #c05621;
    }
    
    .stock-low {
        background: #fed7d7;
        color: #c53030;
    }
    
    .stock-out {
        background: #e2e8f0;
        color: #4a5568;
    }
    
    .price {
        font-weight: 600;
        color: #38a169;
    }
    
    .loading {
        text-align: center;
        padding: 3rem;
        color: #4a5568;
    }
    
    .no-results {
        text-align: center;
        padding: 3rem;
        color: #a0aec0;
    }
    
    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .stat-card {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        text-align: center;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #4a5568;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
</style>
{% endblock %}

{% block content %}
<!-- Sidebar for Cashier -->
<div id="sidebar" class="sidebar">
    <div class="sidebar-header">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <h1 class="sidebar-title">Smart POS</h1>
            <button id="closeSidebar" style="display: none; background: none; border: none; color: white; font-size: 1.25rem; cursor: pointer;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <p id="welcomeText" class="sidebar-welcome">Welcome, Cashier</p>
    </div>
    
    <nav class="sidebar-nav">
        <a href="/pos" class="nav-link">
            <i class="fas fa-cash-register"></i>
            Point of Sale
        </a>
        <a href="/inventory" class="nav-link active">
            <i class="fas fa-boxes"></i>
            View Inventory
        </a>
    </nav>
</div>

<!-- Mobile overlay -->
<div id="overlay" class="overlay"></div>

<!-- Main Content -->
<div class="main-content">
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-left">
                <button id="menuToggle" class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h2 class="page-title">Inventory View</h2>
            </div>
            
            <div class="header-right">
                <div class="user-info">
                    <span id="userName" class="user-name">Cashier</span>
                    <span id="userRole" class="user-role">cashier</span>
                </div>
                
                <button id="logoutBtn" class="logout-btn" title="Logout">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Content Area -->
    <main class="content-area">
        <div class="inventory-container">
            <!-- Stats Row -->
            <div class="stats-row">
                <div class="stat-card">
                    <div id="totalProducts" class="stat-number">-</div>
                    <div class="stat-label">Total Products</div>
                </div>
                <div class="stat-card">
                    <div id="lowStockCount" class="stat-number">-</div>
                    <div class="stat-label">Low Stock Items</div>
                </div>
                <div class="stat-card">
                    <div id="outOfStockCount" class="stat-number">-</div>
                    <div class="stat-label">Out of Stock</div>
                </div>
                <div class="stat-card">
                    <div id="totalValue" class="stat-number">-</div>
                    <div class="stat-label">Total Inventory Value</div>
                </div>
            </div>
            
            <!-- Search Bar -->
            <div class="search-bar">
                <input 
                    type="text" 
                    id="searchInput" 
                    class="search-input" 
                    placeholder="Search products by name, ID, or barcode..."
                >
            </div>
            
            <!-- Inventory Table -->
            <div class="inventory-table">
                <div class="table-header">
                    <i class="fas fa-boxes"></i> Product Inventory
                </div>
                
                <div id="loadingMessage" class="loading" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> Loading inventory...
                </div>
                
                <div id="noResults" class="no-results" style="display: none;">
                    <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>No products found</p>
                </div>
                
                <table id="inventoryTable" class="table">
                    <thead>
                        <tr>
                            <th>Product ID</th>
                            <th>Name</th>
                            <th>Price</th>
                            <th>Stock</th>
                            <th>Status</th>
                            <th>Barcode</th>
                        </tr>
                    </thead>
                    <tbody id="inventoryTableBody">
                        <!-- Products will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>
</div>
{% endblock %}

{% block extra_js %}
<script>
class InventoryViewer {
    constructor() {
        this.products = [];
        this.filteredProducts = [];
        this.searchTimeout = null;
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadUserData();
        this.loadInventory();
    }
    
    setupEventListeners() {
        const searchInput = document.getElementById('searchInput');
        const logoutBtn = document.getElementById('logoutBtn');
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        const closeSidebar = document.getElementById('closeSidebar');
        
        // Search functionality
        searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.filterProducts(e.target.value);
            }, 300);
        });
        
        // Mobile menu
        if (menuToggle) {
            menuToggle.addEventListener('click', () => {
                sidebar.classList.remove('closed');
                overlay.classList.add('show');
            });
        }
        
        if (closeSidebar) {
            closeSidebar.addEventListener('click', () => {
                sidebar.classList.add('closed');
                overlay.classList.remove('show');
            });
        }
        
        if (overlay) {
            overlay.addEventListener('click', () => {
                sidebar.classList.add('closed');
                overlay.classList.remove('show');
            });
        }
        
        // Logout
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.logout());
        }
        
        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 768) {
                sidebar.classList.remove('closed');
                overlay.classList.remove('show');
            } else {
                sidebar.classList.add('closed');
            }
        });
        
        // Initialize sidebar state for mobile
        if (window.innerWidth < 768) {
            sidebar.classList.add('closed');
        }
    }
    
    loadUserData() {
        const userData = localStorage.getItem('user');
        if (userData) {
            const user = JSON.parse(userData);
            document.getElementById('userName').textContent = user.fullName;
            document.getElementById('userRole').textContent = user.role;
            document.getElementById('welcomeText').textContent = `Welcome, ${user.fullName}`;
        }
    }
    
    async loadInventory() {
        this.showLoading(true);
        
        try {
            const response = await fetch('/api/inventory/products', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                }
            });
            
            if (response.ok) {
                this.products = await response.json();
                this.filteredProducts = [...this.products];
                this.displayProducts();
                this.updateStats();
            } else {
                throw new Error('Failed to load inventory');
            }
        } catch (error) {
            console.error('Error loading inventory:', error);
            alert('Failed to load inventory. Please try again.');
        } finally {
            this.showLoading(false);
        }
    }
    
    filterProducts(query) {
        if (!query.trim()) {
            this.filteredProducts = [...this.products];
        } else {
            const searchTerm = query.toLowerCase();
            this.filteredProducts = this.products.filter(product => 
                product.name.toLowerCase().includes(searchTerm) ||
                product.productId?.toLowerCase().includes(searchTerm) ||
                product.barcode?.includes(searchTerm)
            );
        }
        
        this.displayProducts();
    }
    
    displayProducts() {
        const tableBody = document.getElementById('inventoryTableBody');
        const noResults = document.getElementById('noResults');
        const table = document.getElementById('inventoryTable');
        
        if (this.filteredProducts.length === 0) {
            table.style.display = 'none';
            noResults.style.display = 'block';
            return;
        }
        
        table.style.display = 'table';
        noResults.style.display = 'none';
        
        tableBody.innerHTML = this.filteredProducts.map(product => {
            const stockStatus = this.getStockStatus(product.stock);
            return `
                <tr>
                    <td><strong>${product.productId || product._id}</strong></td>
                    <td>${product.name}</td>
                    <td class="price">$${product.price.toFixed(2)}</td>
                    <td><strong>${product.stock}</strong></td>
                    <td><span class="stock-badge ${stockStatus.class}">${stockStatus.text}</span></td>
                    <td>${product.barcode || '-'}</td>
                </tr>
            `;
        }).join('');
    }
    
    getStockStatus(stock) {
        if (stock === 0) {
            return { class: 'stock-out', text: 'Out of Stock' };
        } else if (stock <= 5) {
            return { class: 'stock-low', text: 'Low Stock' };
        } else if (stock <= 20) {
            return { class: 'stock-medium', text: 'Medium Stock' };
        } else {
            return { class: 'stock-high', text: 'In Stock' };
        }
    }
    
    updateStats() {
        const totalProducts = this.products.length;
        const lowStockCount = this.products.filter(p => p.stock <= 5 && p.stock > 0).length;
        const outOfStockCount = this.products.filter(p => p.stock === 0).length;
        const totalValue = this.products.reduce((sum, p) => sum + (p.price * p.stock), 0);
        
        document.getElementById('totalProducts').textContent = totalProducts;
        document.getElementById('lowStockCount').textContent = lowStockCount;
        document.getElementById('outOfStockCount').textContent = outOfStockCount;
        document.getElementById('totalValue').textContent = `$${totalValue.toFixed(2)}`;
    }
    
    showLoading(show) {
        document.getElementById('loadingMessage').style.display = show ? 'block' : 'none';
    }
    
    logout() {
        if (confirm('Are you sure you want to logout?')) {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.href = '/login';
        }
    }
}

// Initialize inventory viewer
const inventoryViewer = new InventoryViewer();
</script>
{% endblock %}
