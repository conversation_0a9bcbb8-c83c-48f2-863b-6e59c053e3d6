{% extends "base.html" %}

{% block title %}Smart POS - Login{% endblock %}

{% block extra_css %}
<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f7fafc;
        padding: 3rem 1rem;
    }
    
    .login-card {
        max-width: 28rem;
        width: 100%;
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        padding: 2rem;
    }
    
    .login-header {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .login-icon {
        margin: 0 auto 1.5rem;
        height: 3rem;
        width: 3rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: #ebf8ff;
    }
    
    .login-title {
        font-size: 1.875rem;
        font-weight: 800;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }
    
    .login-subtitle {
        color: #4a5568;
        font-size: 0.875rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #2d3748;
        margin-bottom: 0.5rem;
    }
    
    .form-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #d2d6dc;
        border-radius: 0.375rem;
        font-size: 1rem;
        transition: border-color 0.2s, box-shadow 0.2s;
    }
    
    .form-input:focus {
        outline: none;
        border-color: #3182ce;
        box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
    }
    
    .login-btn {
        width: 100%;
        background-color: #3182ce;
        color: white;
        padding: 0.75rem;
        border: none;
        border-radius: 0.375rem;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: background-color 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .login-btn:hover {
        background-color: #2c5282;
    }
    
    .login-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    .error-message {
        background-color: #fed7d7;
        border: 1px solid #feb2b2;
        color: #c53030;
        padding: 0.75rem;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
        font-size: 0.875rem;
    }
    
    .demo-credentials {
        text-align: center;
        margin-top: 1.5rem;
        font-size: 0.875rem;
        color: #4a5568;
        background-color: #f7fafc;
        padding: 1rem;
        border-radius: 0.375rem;
    }
    
    .demo-credentials p {
        margin: 0.25rem 0;
    }
    
    .demo-credentials strong {
        color: #2d3748;
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="login-icon">
                <i class="fas fa-cash-register" style="color: #3182ce; font-size: 1.5rem;"></i>
            </div>
            <h2 class="login-title">Smart POS System</h2>
            <p class="login-subtitle">Sign in to your account</p>
        </div>
        
        <form id="loginForm">
            <div id="errorMessage" class="error-message" style="display: none;"></div>
            
            <div class="form-group">
                <label for="username" class="form-label">Username</label>
                <input
                    type="text"
                    id="username"
                    name="username"
                    class="form-input"
                    placeholder="Enter your username"
                    required
                >
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <input
                    type="password"
                    id="password"
                    name="password"
                    class="form-input"
                    placeholder="Enter your password"
                    required
                >
            </div>
            
            <button type="submit" id="loginBtn" class="login-btn">
                <span id="loginBtnText">Sign in</span>
                <i id="loginSpinner" class="fas fa-spinner fa-spin" style="display: none; margin-left: 0.5rem;"></i>
            </button>
        </form>
        
        <div class="demo-credentials">
            <p><strong>Demo Credentials:</strong></p>
            <p><strong>Admin:</strong> admin / admin123</p>
            <p><strong>Cashier:</strong> cashier / cashier123</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const errorMessage = document.getElementById('errorMessage');
    const loginBtn = document.getElementById('loginBtn');
    const loginBtnText = document.getElementById('loginBtnText');
    const loginSpinner = document.getElementById('loginSpinner');

    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        // Show loading state
        loginBtn.disabled = true;
        loginBtnText.textContent = 'Signing in...';
        loginSpinner.style.display = 'inline-block';
        errorMessage.style.display = 'none';
        
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password })
            });
            
            const data = await response.json();
            
            if (response.ok) {
                // Store user data and token
                localStorage.setItem('token', data.token);
                localStorage.setItem('user', JSON.stringify(data.user));

                // Redirect based on user role
                if (data.user.role === 'admin') {
                    window.location.href = '/dashboard';
                } else {
                    window.location.href = '/pos';
                }
            } else {
                throw new Error(data.error || 'Login failed');
            }
        } catch (error) {
            errorMessage.textContent = error.message;
            errorMessage.style.display = 'block';
        } finally {
            // Reset loading state
            loginBtn.disabled = false;
            loginBtnText.textContent = 'Sign in';
            loginSpinner.style.display = 'none';
        }
    });
    
    // Quick login buttons for demo
    document.addEventListener('click', function(e) {
        if (e.target.textContent.includes('admin / admin123')) {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = 'admin123';
        } else if (e.target.textContent.includes('cashier / cashier123')) {
            document.getElementById('username').value = 'cashier';
            document.getElementById('password').value = 'cashier123';
        }
    });
});
</script>
{% endblock %}
