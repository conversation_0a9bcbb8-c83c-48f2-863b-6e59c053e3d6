const Invoice = require('../models/Invoice');
const Product = require('../models/Product');

exports.createInvoice = async (req, res) => {
  try {
    const { items, paymentMethod, customerName, customerContact } = req.body;
    
    // Validate products and calculate totals
    let subtotal = 0;
    const invoiceItems = [];
    
    for (const item of items) {
      const product = await Product.findById(item.product);
      if (!product) {
        return res.status(400).json({ error: `Product ${item.product} not found` });
      }
      
      if (product.stock < item.quantity) {
        return res.status(400).json({ error: `Insufficient stock for ${product.name}` });
      }
      
      const itemTotal = product.price * item.quantity;
      subtotal += itemTotal;
      
      invoiceItems.push({
        product: product._id,
        quantity: item.quantity,
        price: product.price
      });
      
      // Update product stock
      product.stock -= item.quantity;
      await product.save();
    }
    
    const tax = subtotal * 0.1; // Assuming 10% tax
    const total = subtotal + tax;
    
    // Generate invoice number (you might want a better system for this)
    const invoiceNumber = `INV-${Date.now()}`;
    
    const invoice = new Invoice({
      invoiceNumber,
      items: invoiceItems,
      subtotal,
      tax,
      total,
      paymentMethod,
      customerName,
      customerContact,
      cashier: req.user.id
    });
    
    await invoice.save();
    
    res.status(201).json(invoice);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

exports.getInvoices = async (req, res) => {
  try {
    let query = {};
    
    // Cashiers can only see their own invoices
    if (req.user.role === 'cashier') {
      query.cashier = req.user.id;
    }
    
    // Date filtering
    if (req.query.startDate && req.query.endDate) {
      query.date = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate)
      };
    }
    
    const invoices = await Invoice.find(query)
      .populate('items.product')
      .populate('cashier', 'fullName');
      
    res.json(invoices);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

exports.getInvoiceById = async (req, res) => {
  try {
    const invoice = await Invoice.findById(req.params.id)
      .populate('items.product')
      .populate('cashier', 'fullName');
      
    if (!invoice) return res.status(404).json({ error: 'Invoice not found' });
    
    // Cashiers can only access their own invoices
    if (req.user.role === 'cashier' && invoice.cashier._id.toString() !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    res.json(invoice);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

exports.searchProducts = async (req, res) => {
  try {
    const { query } = req.query;
    
    const products = await Product.find({
      $or: [
        { name: { $regex: query, $options: 'i' } },
        { productId: { $regex: query, $options: 'i' } },
        { barcode: query }
      ]
    });
    
    res.json(products);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};