import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalProducts: 0,
    lowStockItems: 0,
    todaySales: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    // Mock data - replace with actual API calls
    setStats({
      totalProducts: 150,
      lowStockItems: 12,
      todaySales: 45,
      totalRevenue: 2850.50
    });
  }, []);

  const StatCard = ({ title, value, icon, color, prefix = '', suffix = '' }) => (
    <div className="card">
      <div className="flex items-center">
        <div className={`p-3 rounded-full ${color} mr-4`}>
          <i className={`${icon} text-white text-xl`}></i>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">
            {prefix}{typeof value === 'number' ? value.toLocaleString() : value}{suffix}
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="card">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Welcome back, {user?.fullName}!
        </h1>
        <p className="text-gray-600">
          Here's what's happening with your store today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Products"
          value={stats.totalProducts}
          icon="fas fa-boxes"
          color="bg-blue-500"
        />
        <StatCard
          title="Low Stock Items"
          value={stats.lowStockItems}
          icon="fas fa-exclamation-triangle"
          color="bg-yellow-500"
        />
        <StatCard
          title="Today's Sales"
          value={stats.todaySales}
          icon="fas fa-shopping-cart"
          color="bg-green-500"
        />
        <StatCard
          title="Total Revenue"
          value={stats.totalRevenue}
          icon="fas fa-dollar-sign"
          color="bg-purple-500"
          prefix="$"
        />
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full btn-primary text-left">
              <i className="fas fa-plus mr-2"></i>
              Add New Product
            </button>
            <button className="w-full btn-secondary text-left">
              <i className="fas fa-cash-register mr-2"></i>
              Start New Sale
            </button>
            <button className="w-full btn-secondary text-left">
              <i className="fas fa-chart-line mr-2"></i>
              View Reports
            </button>
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-3 text-sm">
            <div className="flex items-center text-gray-600">
              <i className="fas fa-shopping-cart text-green-500 mr-2"></i>
              Sale completed - $45.99
            </div>
            <div className="flex items-center text-gray-600">
              <i className="fas fa-plus text-blue-500 mr-2"></i>
              Product added - Coffee Beans
            </div>
            <div className="flex items-center text-gray-600">
              <i className="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
              Low stock alert - Milk
            </div>
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Database</span>
              <span className="text-green-600">
                <i className="fas fa-check-circle mr-1"></i>
                Connected
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Last Backup</span>
              <span className="text-gray-600 text-sm">2 hours ago</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Version</span>
              <span className="text-gray-600 text-sm">v1.0.0</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
