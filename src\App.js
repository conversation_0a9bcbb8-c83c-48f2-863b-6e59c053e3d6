import React, { useEffect, useState } from 'react';
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import axios from 'axios';
import Layout from './components/Layout';
import Login from './components/auth/Login';
import Dashboard from './components/Dashboard';
import POSInterface from './components/pos/POSInterface';
import Inventory from './components/inventory/Inventory';

// Configure axios defaults
axios.defaults.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000';
axios.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

function App() {
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    
    if (token && userData) {
      setUser(JSON.parse(userData));
    }
  }, []);

  const handleLogin = async (credentials) => {
    try {
      const response = await axios.post('/api/auth/login', credentials);
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
      setUser(response.data.user);
      navigate(response.data.user.role === 'admin' ? '/dashboard' : '/pos');
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setUser(null);
    navigate('/login');
  };

  return (
    <div className="App">
      <Routes>
        <Route path="/login" element={
          user ? <Navigate to="/" replace /> : 
          <Login onLogin={handleLogin} />
        } />
        
        <Route path="/" element={
          user ? <Layout user={user} onLogout={handleLogout} /> : 
          <Navigate to="/login" replace />
        }>
          <Route index element={<Navigate to={user?.role === 'admin' ? '/dashboard' : '/pos'} replace />} />
          <Route path="dashboard" element={user?.role === 'admin' ? <Dashboard /> : <Navigate to="/pos" replace />} />
          <Route path="pos" element={<POSInterface />} />
          <Route path="inventory" element={user?.role === 'admin' ? <Inventory /> : <Navigate to="/pos" replace />} />
        </Route>
      </Routes>
    </div>
  );
}

export default App;