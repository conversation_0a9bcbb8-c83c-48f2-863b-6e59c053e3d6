import React, { useState, useEffect } from 'react';
import { Box, Typography, Button, Grid, Paper } from '@mui/material';
import MobileFriendlyIcon from '@mui/icons-material/MobileFriendly';

const TrialDashboard = () => {
  const [daysRemaining, setDaysRemaining] = useState(14);
  
  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setDaysRemaining(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 86400000); // Update every 24 hours
    
    return () => clearInterval(timer);
  }, []);

  return (
    <Box sx={{ 
      maxWidth: 800, 
      mx: 'auto', 
      p: 4,
      backgroundColor: '#f5f7fa',
      borderRadius: 2,
      boxShadow: 3
    }}>
      {/* Header Section */}
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
          {daysRemaining} Days
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Your myBillBook trial period ends in
        </Typography>
        <Typography variant="h5" sx={{ mt: 1, color: '#1976d2', fontWeight: 'medium' }}>
          {daysRemaining} Days
        </Typography>
      </Box>

      {/* Progress Visualization */}
      <Box sx={{ 
        height: 12,
        backgroundColor: '#e0e0e0',
        borderRadius: 6,
        overflow: 'hidden',
        mb: 4
      }}>
        <Box sx={{ 
          width: `${(daysRemaining / 14) * 100}%`,
          height: '100%',
          backgroundColor: '#4caf50',
          transition: 'width 0.5s ease'
        }} />
      </Box>

      {/* Call to Action */}
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Button 
          variant="contained" 
          size="large" 
          sx={{ 
            px: 4,
            py: 1.5,
            fontSize: '1.1rem',
            fontWeight: 'bold',
            borderRadius: 2,
            boxShadow: 2
          }}
        >
          Buy a Plan
        </Button>
      </Box>

      {/* Platform Availability */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        gap: 1,
        mb: 4
      }}>
        <MobileFriendlyIcon color="primary" />
        <Typography variant="body2" color="text.secondary">
          myBillBook is available on Android and iOS
        </Typography>
      </Box>

      {/* Days Counter Grid */}
      <Paper elevation={0} sx={{ p: 3, backgroundColor: 'rgba(25, 118, 210, 0.05)' }}>
        <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold' }}>
          Your MyBillBook Trial Period ends in:
        </Typography>
        <Grid container spacing={1}>
          {Array.from({ length: 14 }, (_, i) => (
            <Grid item xs={3} sm={2} md={1} key={i}>
              <Paper 
                elevation={daysRemaining === (14 - i) ? 3 : 0} 
                sx={{ 
                  p: 1, 
                  textAlign: 'center',
                  backgroundColor: daysRemaining === (14 - i) ? '#e3f2fd' : 'transparent',
                  borderRadius: 1,
                  transition: 'all 0.3s ease'
                }}
              >
                <Typography 
                  variant="body2" 
                  sx={{ 
                    fontWeight: daysRemaining === (14 - i) ? 'bold' : 'normal',
                    color: daysRemaining === (14 - i) ? '#1976d2' : 'inherit'
                  }}
                >
                  {14 - i}.0
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Paper>
    </Box>
  );
};

export default TrialDashboard;