#!/usr/bin/env python3
"""
Smart POS System - Development Server
Run this file to start the Flask development server
"""

from app import app
import os

if __name__ == '__main__':
    # Set development environment
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = '1'
    
    print("🚀 Starting Smart POS System...")
    print("📱 Frontend: http://localhost:5000")
    print("🔌 API: http://localhost:5000/api")
    print("\n📋 Demo Accounts:")
    print("👨‍💼 Admin: admin/admin123 → Dashboard with full access")
    print("👩‍💻 Cashier: cashier/cashier123 → POS interface only")
    print("\n🎯 Features:")
    print("• Admin: Dashboard, POS, Inventory Management, Reports, Settings")
    print("• Cashier: POS Sales, Inventory View (read-only)")
    print("• Auto inventory deduction on sales")
    print("• Role-based access control")
    print("\n" + "="*60)
    
    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True
    )
